using UnityEngine;

/// <summary>
/// Interface for items that can be selected in the selection UI (gems, buffs, etc.)
/// </summary>
public interface ISelectable
{
    string GetDisplayName();
    string GetFormattedDescription();
    Sprite GetIcon();
    Color GetRarityColor();
    SelectionType GetSelectionType();
}

public enum SelectionType
{
    SkillGem,
    SupportGem,
    PlayerBuff
}

/// <summary>
/// Wrapper for GemInstance to implement ISelectable
/// </summary>
public class SelectableGem : ISelectable
{
    private GemInstance gem;
    
    public SelectableGem(GemInstance gemInstance)
    {
        gem = gemInstance;
    }
    
    public GemInstance GetGem() => gem;
    
    public string GetDisplayName() => gem.DisplayName;
    
    public string GetFormattedDescription()
    {
        string description = "";
        
        // Gem Type with color
        if (gem.IsSkillGem)
        {
            description += "<color=#CC3333><b>Skill Gem</b></color>\n";
        }
        else if (gem.IsSupportGem)
        {
            description += "<color=#33CC33><b>Support Gem</b></color>\n";
        }
        
        description += "\n";
        
        // Description
        if (gem.gemDataTemplate != null && !string.IsNullOrEmpty(gem.gemDataTemplate.description))
        {
            description += $"{gem.gemDataTemplate.description}\n\n";
        }
        
        // Stats
        description += "<size=110%><b><color=#FFD700>Stats</color></b></size>\n";
        description += GetFormattedGemStats();
        
        return description;
    }
    
    private string GetFormattedGemStats()
    {
        if (gem.IsSkillGem && gem.gemDataTemplate is SkillGemData skillGem)
        {
            string stats = $"<color=#CCCCCC>Damage:</color> <b>{gem.GetSkillDamage():F0} ({skillGem.damageType})</b>\n" +
                          $"<color=#CCCCCC>Cooldown:</color> <b>{gem.GetSkillCooldown():F1}s</b>\n" +
                          $"<color=#CCCCCC>Mana Cost:</color> <b>{gem.GetSkillManaCost():F0}</b>\n" +
                          $"<color=#CCCCCC>Support Slots:</color> <b>{gem.GetSupportSlotCount()}</b>";

            // Add status effect information if ailment chance > 0
            if (skillGem.ailmentChance > 0)
            {
                stats += $"\n\n<color=#FFD700>Status Effects ({skillGem.ailmentChance:F0}% chance):</color>";

                switch (skillGem.damageType)
                {
                    case DamageType.Fire:
                        // Use default values if not set (for backward compatibility)
                        float ignitePercentToUse = skillGem.ignitePercent > 0 ? skillGem.ignitePercent : 0.2f;
                        float igniteDurationToUse = skillGem.igniteDuration > 0 ? skillGem.igniteDuration : 4f;
                        float totalIgniteDamage = ignitePercentToUse * 100f;
                        stats += $"\n<color=#CCCCCC>• Ignite Damage:</color> <b>{totalIgniteDamage:F0}%</b>";
                        stats += $"\n<color=#CCCCCC>• Ignite Duration:</color> <b>{igniteDurationToUse:F1}s</b>";
                        break;

                    case DamageType.Ice:
                        float freezeSlowToUse = skillGem.freezeSlowAmount > 0 ? skillGem.freezeSlowAmount : 0.5f;
                        float freezeDurationToUse = skillGem.freezeDuration > 0 ? skillGem.freezeDuration : 2f;
                        float slowPercent = freezeSlowToUse * 100f;
                        stats += $"\n<color=#CCCCCC>• Freeze Slow:</color> <b>{slowPercent:F0}%</b>";
                        stats += $"\n<color=#CCCCCC>• Freeze Duration:</color> <b>{freezeDurationToUse:F1}s</b>";
                        break;

                    case DamageType.Physical:
                        float bleedPercentToUse = skillGem.bleedPercent > 0 ? skillGem.bleedPercent : 0.15f;
                        float bleedDurationToUse = skillGem.bleedDuration > 0 ? skillGem.bleedDuration : 6f;
                        float totalBleedDamage = bleedPercentToUse * 100f;
                        stats += $"\n<color=#CCCCCC>• Bleed Damage:</color> <b>{totalBleedDamage:F0}%</b>";
                        stats += $"\n<color=#CCCCCC>• Bleed Duration:</color> <b>{bleedDurationToUse:F1}s</b>";
                        break;

                    case DamageType.Lightning:
                        float shockChainToUse = skillGem.shockChainDamage > 0 ? skillGem.shockChainDamage : 0.1f;
                        float shockRangeToUse = skillGem.shockChainRange > 0 ? skillGem.shockChainRange : 3f;
                        float shockDurationToUse = skillGem.shockDuration > 0 ? skillGem.shockDuration : 2f;
                        float chainPercent = shockChainToUse * 100f;
                        stats += $"\n<color=#CCCCCC>• Shock Damage:</color> <b>{chainPercent:F0}%</b>";
                        stats += $"\n<color=#CCCCCC>• Shock Range:</color> <b>{shockRangeToUse:F1}m</b>";
                        stats += $"\n<color=#CCCCCC>• Shock Duration:</color> <b>{shockDurationToUse:F1}s</b>";
                        break;
                }
            }

            return stats;
        }
        else if (gem.IsSupportGem && gem.gemDataTemplate is SupportGemData supportGem)
        {
            string stats = "";
            
            if (supportGem.damageIncreased != 0f)
                stats += $"<color=#CCCCCC>Increased Damage:</color> <b>{supportGem.damageIncreased:+0;-0}%</b>\n";

            if (supportGem.damageMore != 1f)
                stats += $"<color=#CCCCCC>More Damage:</color> <b>{(supportGem.damageMore - 1f) * 100:+0;-0}%</b>\n";
            
            if (supportGem.cooldownMultiplier != 1f)
                stats += $"<color=#CCCCCC>Cooldown:</color> <b>{(supportGem.cooldownMultiplier - 1f) * 100:+0;-0}%</b>\n";
            
            if (supportGem.manaCostMultiplier != 1f)
                stats += $"<color=#CCCCCC>Mana Cost:</color> <b>{(supportGem.manaCostMultiplier - 1f) * 100:+0;-0}%</b>\n";
            
            if (supportGem.attackSpeedMultiplier != 1f)
                stats += $"<color=#CCCCCC>Attack Speed:</color> <b>{(supportGem.attackSpeedMultiplier - 1f) * 100:+0;-0}%</b>\n";
            
            if (supportGem.addedCritChance != 0f)
                stats += $"<color=#CCCCCC>Crit Chance:</color> <b>+{supportGem.addedCritChance:F1}%</b>\n";
            
            if (supportGem.critMultiplierModifier != 1f)
                stats += $"<color=#CCCCCC>Crit Multiplier:</color> <b>{(supportGem.critMultiplierModifier - 1f) * 100:+0;-0}%</b>\n";
            
            // Special effects
            if (supportGem.addsPierce)
                stats += "<color=#CCCCCC>Pierce:</color> <b>Enabled</b>\n";

            if (supportGem.addsChain)
                stats += $"<color=#CCCCCC>Chain:</color> <b>+{supportGem.chainCount} Times</b>\n";

            if (supportGem.addsAreaDamage)
                stats += $"<color=#CCCCCC>Area Damage:</color> <b>Radius {supportGem.areaRadius}</b>\n";

            if (supportGem.addsFork)
                stats += $"<color=#CCCCCC>Fork:</color> <b>{gem.GetForkCount()} projectiles ({supportGem.forkAngle}° spread)</b>\n";

            if (supportGem.addsMultipleProjectiles)
            {
                stats += $"<color=#CCCCCC>Projectiles:</color> <b>+{gem.GetExtraProjectiles()}";
                if (supportGem.projectileSpreadAngle > 0)
                    stats += $" ({supportGem.projectileSpreadAngle}° spread)";
                stats += "</b>\n";
            }

            // Add Spell Echo support (this was missing!)
            if (supportGem.addsSpellEcho)
            {
                int echoCount = gem.GetSpellEchoCount();
                stats += $"<color=#CCCCCC>Recast Times:</color> <b>{echoCount}</b>\n";
                if (supportGem.echoSpreadRadius > 0)
                    stats += $"<color=#CCCCCC>Recast Radius:</color> <b>{supportGem.echoSpreadRadius}</b>\n";
            }

            // Status effect modifiers
            bool hasStatusEffectModifiers = supportGem.igniteEffectivenessMultiplier != 1f || supportGem.igniteDurationMultiplier != 1f ||
                                           supportGem.freezeEffectivenessMultiplier != 1f || supportGem.freezeDurationMultiplier != 1f ||
                                           supportGem.bleedEffectivenessMultiplier != 1f || supportGem.bleedDurationMultiplier != 1f ||
                                           supportGem.shockEffectivenessMultiplier != 1f || supportGem.shockRangeMultiplier != 1f;

            if (hasStatusEffectModifiers)
            {
                stats += "\n<color=#FFD700>Status Effect Modifiers:</color>\n";

                // Ignite modifiers
                if (supportGem.igniteEffectivenessMultiplier != 1f)
                    stats += $"<color=#CCCCCC>• Ignite Damage:</color> <b>{(supportGem.igniteEffectivenessMultiplier - 1f) * 100:+0;-0}%</b>\n";
                if (supportGem.igniteDurationMultiplier != 1f)
                    stats += $"<color=#CCCCCC>• Ignite Duration:</color> <b>{(supportGem.igniteDurationMultiplier - 1f) * 100:+0;-0}%</b>\n";

                // Freeze modifiers
                if (supportGem.freezeEffectivenessMultiplier != 1f)
                    stats += $"<color=#CCCCCC>• Freeze Effectiveness:</color> <b>{(supportGem.freezeEffectivenessMultiplier - 1f) * 100:+0;-0}%</b>\n";
                if (supportGem.freezeDurationMultiplier != 1f)
                    stats += $"<color=#CCCCCC>• Freeze Duration:</color> <b>{(supportGem.freezeDurationMultiplier - 1f) * 100:+0;-0}%</b>\n";

                // Bleed modifiers
                if (supportGem.bleedEffectivenessMultiplier != 1f)
                    stats += $"<color=#CCCCCC>• Bleed Damage:</color> <b>{(supportGem.bleedEffectivenessMultiplier - 1f) * 100:+0;-0}%</b>\n";
                if (supportGem.bleedDurationMultiplier != 1f)
                    stats += $"<color=#CCCCCC>• Bleed Duration:</color> <b>{(supportGem.bleedDurationMultiplier - 1f) * 100:+0;-0}%</b>\n";

                // Shock modifiers
                if (supportGem.shockEffectivenessMultiplier != 1f)
                    stats += $"<color=#CCCCCC>• Shock Damage:</color> <b>{(supportGem.shockEffectivenessMultiplier - 1f) * 100:+0;-0}%</b>\n";
                if (supportGem.shockRangeMultiplier != 1f)
                    stats += $"<color=#CCCCCC>• Shock Range:</color> <b>{(supportGem.shockRangeMultiplier - 1f) * 100:+0;-0}%</b>\n";
            }

            // Random modifiers
            if (gem.randomModifiers.Count > 0)
            {
                stats += "\n<size=110%><b><color=#FFD700>Modifiers</color></b></size>\n";
                foreach (var modifier in gem.randomModifiers)
                {
                    stats += $"{modifier.GetDisplayString()}\n";
                }
            }

            return stats.TrimEnd('\n');
        }
        
        return "";
    }
    
    public Sprite GetIcon() => gem.gemDataTemplate?.icon;
    
    public Color GetRarityColor() => gem.RarityColor;
    
    public SelectionType GetSelectionType()
    {
        return gem.IsSkillGem ? SelectionType.SkillGem : SelectionType.SupportGem;
    }
}

/// <summary>
/// Wrapper for PlayerBuffData to implement ISelectable
/// </summary>
public class SelectableBuff : ISelectable
{
    private PlayerBuffData buff;
    
    public SelectableBuff(PlayerBuffData buffData)
    {
        buff = buffData;
    }
    
    public PlayerBuffData GetBuff() => buff;
    
    public string GetDisplayName() => buff.GetDisplayName();
    
    public string GetFormattedDescription() => buff.GetFormattedDescription();
    
    public Sprite GetIcon() => buff.icon;
    
    public Color GetRarityColor() => buff.GetRarityColor();
    
    public SelectionType GetSelectionType() => SelectionType.PlayerBuff;
}