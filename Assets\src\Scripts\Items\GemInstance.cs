using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEngine;
using Sirenix.OdinInspector;

[Serializable]
public class GemInstance
{
    [Title("Base Data")]
    [ShowInInspector, ReadOnly]
    public GemData gemDataTemplate;
    
    [ShowInInspector, ReadOnly]
    public string instanceId;
    
    [Title("Progression")]
    [ShowInInspector]
    public GemRarity rarity = GemRarity.Common;
    
    [ShowInInspector]
    public bool isCorrupted = false;
    
    [Title("Random Modifiers")]
    [ShowInInspector]
    [ListDrawerSettings(ShowIndexLabels = false, DraggableItems = false)]
    public List<SupportGemModifier> randomModifiers = new List<SupportGemModifier>();
    
    public GemInstance(GemData template, GemRarity gemRarity = GemRarity.Common, List<SupportGemModifier> modifiers = null)
    {
        gemDataTemplate = template;
        instanceId = Guid.NewGuid().ToString();
        rarity = gemRarity;
        isCorrupted = false;
        randomModifiers = modifiers ?? new List<SupportGemModifier>();
    }
    
    public GemInstance(GemInstance other)
    {
        gemDataTemplate = other.gemDataTemplate;
        instanceId = Guid.NewGuid().ToString();
        rarity = other.rarity;
        isCorrupted = other.isCorrupted;
        // Deep copy modifiers
        randomModifiers = new List<SupportGemModifier>();
        foreach (var mod in other.randomModifiers)
        {
            randomModifiers.Add(new SupportGemModifier(mod.type, mod.value, mod.tier));
        }
    }
    
    #region Calculated Properties
    
    public string DisplayName
    {
        get
        {
        if (gemDataTemplate == null) return "Unknown Gem";
        string name = gemDataTemplate.gemName;
        if (isCorrupted) name = "[Corrupted] " + name;
        return name;
        }
    }
    
    public Sprite Icon => gemDataTemplate?.GetIcon();
    
    public Color RarityColor => GetRarityColor();
    
    private Color GetRarityColor()
    {
        return RarityUtility.GetRarityColor(rarity);
    }
    
    public float GetDamageMultiplier()
    {
        return 1f;
    }
    
    public float GetCooldownMultiplier()
    {
        return 1f;
    }
    
    public float GetManaMultiplier()
    {
        return 1f;
    }
    
    #endregion
    
    #region Skill Gem Properties
    
    public bool IsSkillGem => gemDataTemplate is SkillGemData;
    public bool IsSupportGem => gemDataTemplate is SupportGemData;
    
    public float GetSkillDamage()
    {
        if (gemDataTemplate is SkillGemData skillGem)
        {
        return skillGem.baseDamage * GetDamageMultiplier();
        }
        return 0f;
    }
    
    public float GetSkillCooldown()
    {
        if (gemDataTemplate is SkillGemData skillGem)
        {
        return Mathf.Max(0.1f, skillGem.cooldown * GetCooldownMultiplier());
        }
        return 0f;
    }
    
    public float GetSkillManaCost()
    {
        if (gemDataTemplate is SkillGemData skillGem)
        {
        return skillGem.manaCost * GetManaMultiplier();
        }
        return 0f;
    }
    
    public int GetSupportSlotCount()
    {
        if (gemDataTemplate is SkillGemData skillGem)
        {
        return skillGem.SupportSlotCount;
        }
        return 0;
    }
    
    #endregion
    
    #region Support Gem Properties
    
    public float GetSupportDamageMultiplier()
    {
        if (gemDataTemplate is SupportGemData supportGem)
        {
            // Apply rarity scaling to "more" damage multiplier
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            
            // Scale the bonus part of the multiplier
            float baseBonus = supportGem.damageMore - 1f; // e.g., 1.3 -> 0.3
            float scaledBonus = baseBonus * rarityMultiplier;
            float scaledMultiplier = 1f + scaledBonus; // e.g., 1 + 0.3 = 1.3
            
            // Add random modifier bonuses (treated as "more" multipliers)
            float randomBonus = GetRandomModifierBonus(SupportGemModifierType.DamageMultiplier);
            
            // Convert percentage to multiplier (e.g., +20% = 1.2x)
            float totalMultiplier = scaledMultiplier * (1f + randomBonus / 100f);
            
            return totalMultiplier;
        }
        return 1f;
    }
    
    /// <summary>
    /// Get increased damage bonus from support gem (additive percentage)
    /// </summary>
    public float GetSupportIncreasedDamage()
    {
        if (gemDataTemplate is SupportGemData supportGem)
        {
            // Get base damage with rarity scaling
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            float scaledDamage = supportGem.damageIncreased * rarityMultiplier;
            
            // Return the scaled damage
            return scaledDamage;
            
            // Note: Random modifiers for "increased damage" could be added here
            // if we add a new SupportGemModifierType.DamageIncreased
        }
        return 0f;
    }
    
    public float GetSupportCooldownMultiplier()
    {
        if (gemDataTemplate is SupportGemData supportGem)
        {
            float baseMultiplier = supportGem.cooldownMultiplier;
            
            // Add random modifier bonuses (cooldown reduction is negative)
            float randomReduction = GetRandomModifierBonus(SupportGemModifierType.CooldownReduction);
            
            // Apply reduction (e.g., -20% cooldown = 0.8x multiplier)
            float totalMultiplier = baseMultiplier * (1f - randomReduction / 100f);
            
            return Mathf.Max(0.1f, totalMultiplier); // Cap at 90% reduction
        }
        return 1f;
    }
    
    /// <summary>
    /// Get total bonus value for a specific modifier type from random modifiers
    /// </summary>
    public float GetRandomModifierBonus(SupportGemModifierType type)
    {
        return randomModifiers.Where(m => m.type == type).Sum(m => m.value);
    }
    
    /// <summary>
    /// Get support gem's added critical chance including random modifiers
    /// </summary>
    public float GetSupportCritChanceBonus()
    {
        float baseBonus = 0f;
        if (gemDataTemplate is SupportGemData supportGem)
        {
            baseBonus = supportGem.addedCritChance;
        }
        
        return baseBonus + GetRandomModifierBonus(SupportGemModifierType.CriticalChance);
    }
    
    /// <summary>
    /// Get support gem's critical multiplier modifier including random modifiers
    /// </summary>
    public float GetSupportCritMultiplierModifier()
    {
        float baseModifier = 1f;
        if (gemDataTemplate is SupportGemData supportGem)
        {
            // Apply rarity scaling to crit multiplier
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            float baseBonus = supportGem.critMultiplierModifier - 1f;
            float scaledBonus = baseBonus * rarityMultiplier;
            baseModifier = 1f + scaledBonus;
        }
        
        // Apply random crit multiplier bonus
        float randomBonus = GetRandomModifierBonus(SupportGemModifierType.CriticalMultiplier);
        return baseModifier * (1f + randomBonus / 100f);
    }
    
    /// <summary>
    /// Get support gem's attack speed multiplier including random modifiers
    /// </summary>
    public float GetSupportAttackSpeedMultiplier()
    {
        float baseMultiplier = 1f;
        if (gemDataTemplate is SupportGemData supportGem)
        {
            // Apply rarity scaling to attack speed
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            float baseBonus = supportGem.attackSpeedMultiplier - 1f;
            float scaledBonus = baseBonus * rarityMultiplier;
            baseMultiplier = 1f + scaledBonus;
        }
        
        // Apply random attack speed bonus
        float randomBonus = GetRandomModifierBonus(SupportGemModifierType.AttackSpeedMultiplier);
        return baseMultiplier * (1f + randomBonus / 100f);
    }
    
    /// <summary>
    /// Get total extra projectiles from random modifiers
    /// </summary>
    public int GetExtraProjectiles()
    {
        int baseExtra = 0;
        if (gemDataTemplate is SupportGemData supportGem && supportGem.addsMultipleProjectiles)
        {
            // Apply rarity scaling
            float multiplier = supportGem.GetRarityMultiplier(rarity);
            if (supportGem.UsesIntegerScaling)
            {
                baseExtra = Mathf.FloorToInt(supportGem.extraProjectiles * multiplier);
            }
            else
            {
                baseExtra = supportGem.extraProjectiles; // No scaling for non-integer mode
            }
        }
        
        return baseExtra + Mathf.RoundToInt(GetRandomModifierBonus(SupportGemModifierType.ExtraProjectiles));
    }
    
    /// <summary>
    /// Get flat damage bonus from random modifiers
    /// </summary>
    public float GetSupportFlatDamageBonus()
    {
        return GetRandomModifierBonus(SupportGemModifierType.DamageFlat);
    }
    
    /// <summary>
    /// Get mana cost multiplier including random modifiers
    /// </summary>
    public float GetSupportManaCostMultiplier()
    {
        float baseMultiplier = 1f;
        if (gemDataTemplate is SupportGemData supportGem)
        {
            // Apply rarity scaling to mana cost
            float rarityMultiplier = supportGem.GetRarityMultiplier(rarity);
            // For mana cost, we want to scale the penalty/bonus appropriately
            if (supportGem.manaCostMultiplier > 1f)
            {
                // It's a penalty - scale it down for better rarities
                float penalty = supportGem.manaCostMultiplier - 1f;
                penalty /= rarityMultiplier; // Better rarity = less penalty
                baseMultiplier = 1f + penalty;
            }
            else
            {
                // It's a reduction - scale it up for better rarities
                float reduction = 1f - supportGem.manaCostMultiplier;
                reduction *= rarityMultiplier; // Better rarity = more reduction
                baseMultiplier = 1f - reduction;
            }
        }
        
        // Apply mana cost reduction (negative modifier)
        float randomReduction = GetRandomModifierBonus(SupportGemModifierType.ManaCostReduction);
        return baseMultiplier * (1f - randomReduction / 100f);
    }
    
    /// <summary>
    /// Get chain count including rarity scaling
    /// </summary>
    public int GetChainCount()
    {
        int baseChains = 0;
        if (gemDataTemplate is SupportGemData supportGem && supportGem.addsChain)
        {
            // Apply rarity scaling
            float multiplier = supportGem.GetRarityMultiplier(rarity);
            if (supportGem.UsesIntegerScaling)
            {
                baseChains = Mathf.FloorToInt(supportGem.chainCount * multiplier);
            }
            else
            {
                baseChains = supportGem.chainCount; // No scaling for non-integer mode
            }
        }
        
        // Note: Could add random modifiers for chain count here if needed
        return baseChains;
    }
    
    /// <summary>
    /// Get area radius including rarity scaling
    /// </summary>
    public float GetAreaRadius()
    {
        float baseRadius = 0f;
        if (gemDataTemplate is SupportGemData supportGem && supportGem.addsAreaDamage)
        {
            // Apply rarity scaling if integer scaling is enabled
            if (supportGem.UsesIntegerScaling)
            {
                float multiplier = supportGem.GetRarityMultiplier(rarity);
                baseRadius = supportGem.areaRadius * multiplier;
            }
            else
            {
                baseRadius = supportGem.areaRadius;
            }
        }
        
        return baseRadius;
    }
    
    /// <summary>
    /// Get fork count including rarity scaling
    /// </summary>
    public int GetForkCount()
    {
        int baseForks = 0;
        if (gemDataTemplate is SupportGemData supportGem && supportGem.addsFork)
        {
            // Apply rarity scaling
            float multiplier = supportGem.GetRarityMultiplier(rarity);
            if (supportGem.UsesIntegerScaling)
            {
                baseForks = Mathf.FloorToInt(supportGem.forkCount * multiplier);
            }
            else
            {
                baseForks = supportGem.forkCount; // No scaling for non-integer mode
            }
        }
        
        return baseForks;
    }
    
    /// <summary>
    /// Get fork angle
    /// </summary>
    public float GetForkAngle()
    {
        if (gemDataTemplate is SupportGemData supportGem && supportGem.addsFork)
        {
            return supportGem.forkAngle;
        }
        
        return 30f; // Default fork angle
    }
    
    /// <summary>
    /// Get spell echo count including rarity scaling
    /// </summary>
    public int GetSpellEchoCount()
    {
        int baseEchoes = 0;
        if (gemDataTemplate is SupportGemData supportGem && supportGem.addsSpellEcho)
        {
            // Apply rarity scaling if integer scaling is enabled
            if (supportGem.UsesIntegerScaling)
            {
                float multiplier = supportGem.GetRarityMultiplier(rarity);
                baseEchoes = Mathf.FloorToInt(supportGem.echoCount * multiplier);
            }
            else
            {
                baseEchoes = supportGem.echoCount;
            }
        }
        
        return baseEchoes;
    }
    
    #endregion
    
    public string GetTooltipText()
    {
        if (gemDataTemplate == null) return "Unknown Gem";
        
        StringBuilder sb = new StringBuilder(512); // Pre-allocate reasonable capacity
        
        // Header
        sb.AppendFormat("<size=120%><b><color=#{0}>{1}</color></b></size>", 
            ColorUtility.ToHtmlStringRGB(RarityColor), DisplayName);
        sb.AppendFormat("\n{0} {1} Gem", rarity, IsSkillGem ? "Skill" : "Support");
        
        // Add gem tags display
        if (IsSkillGem && gemDataTemplate is SkillGemData skillData && skillData.gemTags != GemTag.None)
        {
            var tagNames = new System.Collections.Generic.List<string>();
            if ((skillData.gemTags & GemTag.Melee) != 0) tagNames.Add(GemTag.Melee.GetColoredDisplayName());
            if ((skillData.gemTags & GemTag.Projectile) != 0) tagNames.Add(GemTag.Projectile.GetColoredDisplayName());
            if ((skillData.gemTags & GemTag.Spell) != 0) tagNames.Add(GemTag.Spell.GetColoredDisplayName());
            sb.Append("\nTags: ");
            sb.Append(string.Join(", ", tagNames));
        }
        else if (IsSupportGem && gemDataTemplate is SupportGemData supportData && supportData.compatibleTags != GemTag.None)
        {
            var tagNames = new System.Collections.Generic.List<string>();
            if ((supportData.compatibleTags & GemTag.Melee) != 0) tagNames.Add(GemTag.Melee.GetColoredDisplayName());
            if ((supportData.compatibleTags & GemTag.Projectile) != 0) tagNames.Add(GemTag.Projectile.GetColoredDisplayName());
            if ((supportData.compatibleTags & GemTag.Spell) != 0) tagNames.Add(GemTag.Spell.GetColoredDisplayName());
            sb.Append("\nCompatible with: ");
            sb.Append(string.Join(", ", tagNames));
        }
        
        if (!string.IsNullOrEmpty(gemDataTemplate.description))
        {
            sb.Append("\n\n");
            sb.Append(gemDataTemplate.description);
        }
        
        if (IsSkillGem && gemDataTemplate is SkillGemData skillStats)
        {
            sb.Append("\n\n<size=110%><b><color=#FFD700>Stats</color></b></size>");
            sb.AppendFormat("\n<color=#CCCCCC>Damage:</color> <b>{0:F0}</b>", GetSkillDamage());
            sb.AppendFormat("\n<color=#CCCCCC>Cooldown:</color> <b>{0:F1}s</b>", GetSkillCooldown());
            sb.AppendFormat("\n<color=#CCCCCC>Mana Cost:</color> <b>{0:F0}</b>", GetSkillManaCost());
            sb.AppendFormat("\n<color=#CCCCCC>Attack Speed:</color> <b>{0:F1}x</b>", skillStats.attackSpeedMultiplier);
            sb.AppendFormat("\n<color=#CCCCCC>Critical Chance:</color> <b>{0:F1}%</b>", skillStats.critChance);
            sb.AppendFormat("\n<color=#CCCCCC>Critical Multiplier:</color> <b>{0:F1}x</b>", skillStats.critMultiplier);
            sb.AppendFormat("\n<color=#CCCCCC>Support Slots:</color> <b>{0}</b>", GetSupportSlotCount());

            // Add status effect information based on damage type and ailment chance
            if (skillStats.ailmentChance > 0)
            {
                sb.Append("\n\n<color=#FFD700>Status Effects (");
                sb.Append(skillStats.ailmentChance.ToString("F0"));
                sb.Append("% chance):</color>");

                switch (skillStats.damageType)
                {
                    case DamageType.Fire:
                        // Use default values if not set (for backward compatibility)
                        float ignitePercentToUse = skillStats.ignitePercent > 0 ? skillStats.ignitePercent : 0.2f;
                        float igniteDurationToUse = skillStats.igniteDuration > 0 ? skillStats.igniteDuration : 4f;
                        float totalIgniteDamage = ignitePercentToUse * 100f;
                        sb.AppendFormat("\n<color=#FF6B35>• Ignite:</color> {0:F0}% damage over {1:F1}s", totalIgniteDamage, igniteDurationToUse);
                        break;

                    case DamageType.Ice:
                        float freezeSlowToUse = skillStats.freezeSlowAmount > 0 ? skillStats.freezeSlowAmount : 0.5f;
                        float freezeDurationToUse = skillStats.freezeDuration > 0 ? skillStats.freezeDuration : 2f;
                        float slowPercent = freezeSlowToUse * 100f;
                        sb.AppendFormat("\n<color=#4FC3F7>• Freeze:</color> {0:F0}% slow for {1:F1}s", slowPercent, freezeDurationToUse);
                        break;

                    case DamageType.Physical:
                        float bleedPercentToUse = skillStats.bleedPercent > 0 ? skillStats.bleedPercent : 0.15f;
                        float bleedDurationToUse = skillStats.bleedDuration > 0 ? skillStats.bleedDuration : 6f;
                        float totalBleedDamage = bleedPercentToUse * 100f;
                        sb.AppendFormat("\n<color=#8B0000>• Bleed:</color> {0:F0}% damage over {1:F1}s", totalBleedDamage, bleedDurationToUse);
                        break;

                    case DamageType.Lightning:
                        float shockChainToUse = skillStats.shockChainDamage > 0 ? skillStats.shockChainDamage : 0.1f;
                        float shockRangeToUse = skillStats.shockChainRange > 0 ? skillStats.shockChainRange : 3f;
                        float shockDurationToUse = skillStats.shockDuration > 0 ? skillStats.shockDuration : 2f;
                        float chainPercent = shockChainToUse * 100f;
                        sb.AppendFormat("\n<color=#FFE082>• Shock:</color> {0:F0}% chain damage, {1:F1}m range for {2:F1}s", chainPercent, shockRangeToUse, shockDurationToUse);
                        break;
                }
            }
        }
        else if (IsSupportGem && gemDataTemplate is SupportGemData supportData)
        {
            bool hasMainStats = false;
            
            // Show damage modifiers
            if (supportData.damageIncreased != 0f)
            {
                if (!hasMainStats) { sb.Append("\n\n<size=110%><b><color=#FFD700>Stats</color></b></size>"); hasMainStats = true; }
                sb.AppendFormat("\n<color=#CCCCCC>Increased Damage:</color> <b>{0:+0;-0}%</b>", GetSupportIncreasedDamage());
            }
            if (supportData.damageMore != 1f)
            {
                if (!hasMainStats) { sb.Append("\n\n<size=110%><b><color=#FFD700>Stats</color></b></size>"); hasMainStats = true; }
                float pct = (GetSupportDamageMultiplier() - 1f) * 100f;
                sb.AppendFormat("\n<color=#CCCCCC>More Damage:</color> <b>{0:+0;-0}%</b>", pct);
            }
            if (supportData.cooldownMultiplier != 1f)
            {
                if (!hasMainStats) { sb.Append("\n\n<size=110%><b><color=#FFD700>Stats</color></b></size>"); hasMainStats = true; }
                float pct = (GetSupportCooldownMultiplier() - 1f) * 100f;
                sb.AppendFormat("\n<color=#CCCCCC>Cooldown:</color> <b>{0:+0;-0}%</b>", pct);
            }
            if (supportData.manaCostMultiplier != 1f)
            {
                if (!hasMainStats) { sb.Append("\n\n<size=110%><b><color=#FFD700>Stats</color></b></size>"); hasMainStats = true; }
                float pct = (GetSupportManaCostMultiplier() - 1f) * 100f;
                sb.AppendFormat("\n<color=#CCCCCC>Mana Cost:</color> <b>{0:+0;-0}%</b>", pct);
            }
            if (supportData.attackSpeedMultiplier != 1f)
            {
                if (!hasMainStats) { sb.Append("\n\n<size=110%><b><color=#FFD700>Stats</color></b></size>"); hasMainStats = true; }
                float pct = (GetSupportAttackSpeedMultiplier() - 1f) * 100f;
                sb.AppendFormat("\n<color=#CCCCCC>Attack Speed:</color> <b>{0:+0;-0}%</b>", pct);
            }

            // Show additive modifiers
            if (supportData.addedCritChance != 0f)
            {
                if (!hasMainStats) { sb.Append("\n\n<size=110%><b><color=#FFD700>Stats</color></b></size>"); hasMainStats = true; }
                sb.AppendFormat("\n<color=#CCCCCC>Critical Chance:</color> <b>+{0:F1}%</b>", GetSupportCritChanceBonus());
            }
            if (supportData.critMultiplierModifier != 1f)
            {
                if (!hasMainStats) { sb.Append("\n\n<size=110%><b><color=#FFD700>Stats</color></b></size>"); hasMainStats = true; }
                float pct = (GetSupportCritMultiplierModifier() - 1f) * 100f;
                sb.AppendFormat("\n<color=#CCCCCC>Critical Multiplier:</color> <b>{0:+0;-0}%</b>", pct);
            }
            
            // Show special effects
            bool hasSpecialEffects = false;
            if (supportData.addsPierce)
            {
                if (!hasSpecialEffects) { sb.Append("\n\n<size=110%><b><color=#FFD700>Base Mods</color></b></size>"); hasSpecialEffects = true; }
                sb.Append("\n<color=#CCCCCC>Pierce:</color> <b>Enabled</b>");
            }
            if (supportData.addsChain)
            {
                if (!hasSpecialEffects) { sb.Append("\n\n<size=110%><b><color=#FFD700>Base Mods</color></b></size>"); hasSpecialEffects = true; }
                int chainCount = GetChainCount();
                sb.AppendFormat("\n<color=#CCCCCC>Chain:</color> <b>+{0} Times</b>", chainCount);
            }
            if (supportData.addsAreaDamage)
            {
                if (!hasSpecialEffects) { sb.Append("\n\n<size=110%><b><color=#FFD700>Base Mods</color></b></size>"); hasSpecialEffects = true; }
                float areaRadius = GetAreaRadius();
                sb.AppendFormat("\n<color=#CCCCCC>Area Damage:</color> <b>Radius {0:F1}</b>", areaRadius);
            }
            if (supportData.addsFork)
            {
                if (!hasSpecialEffects) { sb.Append("\n\n<size=110%><b><color=#FFD700>Base Mods</color></b></size>"); hasSpecialEffects = true; }
                int forkCount = GetForkCount();
                sb.AppendFormat("\n<color=#CCCCCC>Fork:</color> <b>{0} projectiles ({1}° spread)</b>", forkCount, supportData.forkAngle);
            }
            if (supportData.addsMultipleProjectiles)
            {
                if (!hasSpecialEffects) { sb.Append("\n\n<size=110%><b><color=#FFD700>Base Mods</color></b></size>"); hasSpecialEffects = true; }
                int totalProjectiles = GetExtraProjectiles();
                sb.AppendFormat("\n<color=#CCCCCC>Projectiles:</color> <b>+{0}", totalProjectiles);
                if (supportData.projectileSpreadAngle > 0)
                    sb.AppendFormat(" ({0}° spread)", supportData.projectileSpreadAngle);
                sb.Append("</b>");
            }
            if (supportData.addsSpellEcho)
            {
                if (!hasSpecialEffects) { sb.Append("\n\n<size=110%><b><color=#FFD700>Base Mods</color></b></size>"); hasSpecialEffects = true; }
                int echoCount = GetSpellEchoCount();
                sb.AppendFormat("\n<color=#CCCCCC>Recast Times:</color> <b>{0}</b>", echoCount);
                if (supportData.echoSpreadRadius > 0)
                    sb.AppendFormat("\n<color=#CCCCCC>Recast Radius:</color> <b>{0}</b>", supportData.echoSpreadRadius);
            }

            // Add status effect modifiers if any are present
            bool hasStatusEffectModifiers = supportData.igniteEffectivenessMultiplier != 1f || supportData.igniteDurationMultiplier != 1f ||
                                           supportData.freezeEffectivenessMultiplier != 1f || supportData.freezeDurationMultiplier != 1f ||
                                           supportData.bleedEffectivenessMultiplier != 1f || supportData.bleedDurationMultiplier != 1f ||
                                           supportData.shockEffectivenessMultiplier != 1f || supportData.shockRangeMultiplier != 1f;

            if (hasStatusEffectModifiers)
            {
                if (!hasSpecialEffects) { sb.Append("\n\n<size=110%><b><color=#FFD700>Base Mods</color></b></size>"); hasSpecialEffects = true; }
                sb.Append("\n\n<color=#FFD700>Status Effect Modifiers:</color>");

                // Ignite modifiers
                if (supportData.igniteEffectivenessMultiplier != 1f)
                {
                    float pct = (supportData.igniteEffectivenessMultiplier - 1f) * 100f;
                    sb.AppendFormat("\n<color=#FF6B35>• Ignite Damage:</color> {0:+0;-0}%", pct);
                }
                if (supportData.igniteDurationMultiplier != 1f)
                {
                    float pct = (supportData.igniteDurationMultiplier - 1f) * 100f;
                    sb.AppendFormat("\n<color=#FF6B35>• Ignite Duration:</color> {0:+0;-0}%", pct);
                }

                // Freeze modifiers
                if (supportData.freezeEffectivenessMultiplier != 1f)
                {
                    float pct = (supportData.freezeEffectivenessMultiplier - 1f) * 100f;
                    sb.AppendFormat("\n<color=#4FC3F7>• Freeze Effectiveness:</color> {0:+0;-0}%", pct);
                }
                if (supportData.freezeDurationMultiplier != 1f)
                {
                    float pct = (supportData.freezeDurationMultiplier - 1f) * 100f;
                    sb.AppendFormat("\n<color=#4FC3F7>• Freeze Duration:</color> {0:+0;-0}%", pct);
                }

                // Bleed modifiers
                if (supportData.bleedEffectivenessMultiplier != 1f)
                {
                    float pct = (supportData.bleedEffectivenessMultiplier - 1f) * 100f;
                    sb.AppendFormat("\n<color=#8B0000>• Bleed Damage:</color> {0:+0;-0}%", pct);
                }
                if (supportData.bleedDurationMultiplier != 1f)
                {
                    float pct = (supportData.bleedDurationMultiplier - 1f) * 100f;
                    sb.AppendFormat("\n<color=#8B0000>• Bleed Duration:</color> {0:+0;-0}%", pct);
                }

                // Shock modifiers
                if (supportData.shockEffectivenessMultiplier != 1f)
                {
                    float pct = (supportData.shockEffectivenessMultiplier - 1f) * 100f;
                    sb.AppendFormat("\n<color=#FFE082>• Shock Damage:</color> {0:+0;-0}%", pct);
                }
                if (supportData.shockRangeMultiplier != 1f)
                {
                    float pct = (supportData.shockRangeMultiplier - 1f) * 100f;
                    sb.AppendFormat("\n<color=#FFE082>• Shock Range:</color> {0:+0;-0}%", pct);
                }
            }
        }
        
        // Add random modifiers to tooltip
        if (randomModifiers.Count > 0)
        {
            sb.Append("\n\n<size=110%><b><color=#FFD700>Sub Mods</color></b></size>");
            foreach (var modifier in randomModifiers)
            {
                sb.Append("\n");
                sb.Append(modifier.GetDisplayString());
            }
        }
        
        if (isCorrupted)
            sb.Append("\n\n<b><color=#8B0000>Corrupted - Cannot be modified</color></b>");
        
        return sb.ToString();
    }
    
    #region Equality
    
    public override bool Equals(object obj)
    {
        if (obj is GemInstance other)
        {
        return instanceId == other.instanceId;
        }
        return false;
    }
    
    public override int GetHashCode()
    {
        return instanceId?.GetHashCode() ?? 0;
    }
    
    #endregion
}